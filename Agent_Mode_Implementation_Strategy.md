# Agent Mode Implementation Strategy

*Last updated: 2025-09-29*

This document lays out a comprehensive, step-by-step plan for introducing the advanced **Agent Mode** tool into the existing Local(o)llama chatbot. The plan intentionally reuses the current architecture (Tkinter UI, `OllamaChat`, `Settings`, `ConversationManager`, `PromptManager`, `ToolsManager`, MCP infrastructure, etc.) and avoids introducing new external dependencies. Every step below can be executed using capabilities that already exist inside the repository.

---

## 1. Objectives and Success Criteria

- **Opt-in agent sequencing**: When the "Agent Tool" control is active, the chatbot should allow users to stage multiple agents (distinct instruction + model + tool configurations) before launching them sequentially.
- **Zero-regression default**: With the Agent Tool disabled, the app behaves exactly as it does today.
- **Clear in-UI feedback**: Users always know which step of the agent journey they are in (“Agent Sequence: Begin”, “First Agent Defined”, …, “Agent 1 Task Complete”, etc.).
- **Reusable agent sequences**: Users can save, load, and manage agent sequences similar to prompts or conversations.
- **Cross-agent awareness**: Each agent can reference others by title placeholder (e.g., `{{Agent-4}}`) and resolve those references at execution time.
- **No extra installs**: Leverage existing JSON persistence, Tkinter widgets, background threads, and messaging pipeline.

Success is measured by: (a) user acceptance tests for the new workflow, (b) non-regression automated tests for standard chat, (c) ability to stage, save, reload, and execute multi-agent sequences without errors.

---

## 2. Current Capabilities to Reuse

| Capability                                       | Existing Module(s)                                                                                     | How It Helps Agent Mode                                                                                                                             |
| ------------------------------------------------ | ------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------- |
| Persistent settings (booleans, floats, API keys) | `settings.py`, `OllamaChat.init_variables()`, vendor-specific config classes                       | Persist Agent Mode defaults, slider values, provider selections, and proprietary API credentials so staged agents replay with the same environment. |
| Tkinter sidebar controls & status messaging      | `OllamaChat.create_sidebar()`, `display_message()`                                                 | Add Agent Mode checkbox/dropdown, surface “Agent Sequence: Begin”, etc.                                                                           |
| Chat capture & rendering                         | `ConversationManager`, `OllamaChat.send_message()`                                                 | Capture staged agent instructions without sending to model; reuse rendering pipeline for status updates.                                            |
| Multi-provider model orchestration               | `models_manager.py`, `OllamaManager`, `GeminiManager`, `DeepSeekManager`, `AnthropicManager` | Persist and replay each agent’s chosen vendor/model parameters, whether local Ollama or proprietary APIs.                                          |
| Prompt storage & retrieval                       | `prompt_manager.py`                                                                                  | Blueprint for persisting agent sequences; same JSON-on-disk pattern.                                                                                |
| Tool execution + status                          | `ToolsManager`, `ToolStatusPanel`                                                                  | Display background status for each agent run if desired; reuse `ToolTask` for long-running steps.                                                 |
| MCP overlays & panel toggles                     | `mcp_ui.py`, `OllamaChat.show_mcp_panel()`                                                          | Reference UX patterns for optional panel/dropdown presentation.                                                                                     |
| Threaded response handling                       | `OllamaChat.get_response()`, per-provider `ModelManager.get_response()` implementations            | Coordinate background execution for any provider (Ollama, Gemini, DeepSeek, Anthropic), ensuring UI responsiveness while agents stream results.     |
| Temporary file utilities                         | `tempfile` usage already present in `mcp_file_import.py`                                           | Cache per-agent JSON payloads without new dependencies.                                                                                             |

---

## 3. User Journey Overview

1. **Baseline (Agent Tool unchecked)** – identical behavior to current application.
2. **Activate Agent Mode** – user toggles the Agent control -> UI shows `Agent Sequence: Begin`. Inputs are captured rather than sent immediately.
3. **Define Agent(s)** – each press of "Send" while enabled caches a message configuration (model, temps, system prompt, tools, RAG toggles, etc.) and updates UI status ("First Agent Defined", "Second Agent Defined", …).
4. **Confirm Staging** – once the user unticks the Agent Tool, the chat displays `N Agents Defined. View Configuration Window to Begin...` and the Configure button becomes enabled (the criteria—Agent Mode was active, at least one agent exists, checkbox now off—are all met).
5. **Configure Sequence** – when the Configure button becomes active (Agent Tool was ticked, at least one agent exists, checkbox now unchecked), the user opens a dedicated management window to review, rename, reorder, load, or save agents.
6. **Run Sequence** – from the configuration window the user triggers `Run Agent`, which auto-saves the sequence under the latest name, executes agents sequentially, and streams completion updates ("Agent 1 Task Complete...", etc.).
7. **Save for Later** – the configuration window exposes Save/Load workflow backed by JSON metadata in the repository so staged sequences can be reused or shared.

---

## 4. Data & State Model

### 4.1 In-Memory Structures (within `OllamaChat`)

- `self.agent_mode_var`: `tk.BooleanVar` managed in `init_variables()` and persisted via `Settings`.
- `self.armed_agents`: ordered list of agent definitions staged during Agent Mode.
- `self.active_agent_sequence_name`: optional identifier when loading/saving predefined sequences.
- Provider routing continues to use the existing model manager registry: the `developer` value stored with each agent (e.g., `"ollama"`, `"google"`, `"deepseek"`, `"anthropic"`) determines which `ModelManager` subclass (`OllamaManager`, `GeminiManager`, `DeepSeekManager`, `AnthropicManager`) will execute the agent while sharing the same memory/context assembly pipeline.

### 4.2 Agent Definition Schema

Reuse the JSON patterns already handled by `PromptManager` & `ConversationManager`. One agent entry should capture:

```json
{
  "title": "First-Agent",           // auto or user supplied
  "model": "llama3:70b",            // from current model selector
  "developer": "ollama",            // developer dropdown value (e.g., 'ollama', 'google', 'deepseek', 'anthropic')
  "system": "...",                  // system textbox contents
  "messages": [...],                 // user prompt & any tool directives
  "parameters": {                    // sliders and toggles (temp, top_p, etc.)
    "temperature": 0.7,
    "top_p": 0.9,
    "include_chat": true,
    ...
  },
  "tools": {
    "read_file": false,
    "write_file": true,
    ...
  },
  "metadata": {
    "index": 1,
    "timestamp": "2025-09-29T...",
    "references": ["Agent-2", "Agent-4"]
  }
}
```

This mirrors the structures already serialized in `ConversationManager` (`messages`) and `PromptManager` (`metadata`). Because the `developer` field persists the provider choice, every agent—local or proprietary—reuses the same RAG context, MCP memories, file attachments, and chat history assembly before the appropriate `ModelManager.get_response()` is invoked.

### 4.3 Persistence Strategy

- **Temporary cache**: Store active sequences in a deterministic JSON file under `tempfile.gettempdir()` (as detailed in Section 10) so staged agents persist until the session is cleared.
- **Long-term storage**: extend `PromptManager` to create an `AgentSequence` helper that writes to a dedicated `agents/` directory using the same JSON-on-disk pattern. No new library needed—just an additional method to load/save Agent JSON independent of prompts or conversation history.

---

## 5. UI & UX Integration Plan

### Step 1 – Sidebar Control

- Add `self.agent_mode_var = tk.BooleanVar(...)` inside `init_variables()` using `self.settings.get("agent_mode_enabled", False)` and track `self.configure_agents_button` state for later enable/disable.
- In `create_sidebar()`, under the "Options" `CollapsibleFrame`, insert a checkbox labeled “Agent Tool” plus a sibling **Configure Agents** button. Bind the checkbox to `self.on_agent_mode_toggle`. During initialization, inspect the cached agent list (see Section 10); if it contains one or more agents, enable the Configure Agents button immediately so the sequence can be reviewed without touching the Agent Tool toggle. Otherwise leave it disabled.
- When the user toggles the Agent Tool **on**, call `display_message("\nAgent Sequence: Begin\n", 'status')`, clear `self.armed_agents`, set `self.active_agent_sequence_name = None`, and temporarily disable the Configure Agents button to avoid conflicts while staging. Once staging ends (Agent Tool toggled off) or cached agents are detected again, re-enable the button.

### Step 2 – Status Feedback

- Reuse `display_message` for chat log updates as agents are defined (“First Agent Defined”, etc.), and surface context in `status_bar["text"]` (“Defining Agent 2...” ).
- On the transition where Agent Mode is turned **off** while agents exist, emit `display_message(f"\n{len(self.armed_agents)} Agents Defined. View Configuration Window to Begin...\n", 'status')`, update the status bar accordingly, and (re)enable the Configure Agents button if it was disabled during staging.

### Step 3 – Sequence Management UI

- Clicking the **Configure Agents** button opens a Toplevel patterned after the MCP panel. The button is enabled whenever at least one agent definition is available (cached or freshly staged) and Agent Mode is not actively collecting a new agent; otherwise it remains disabled.
- The window presents the agents in order (e.g., Listbox/Treeview with rows “Agent 1 – Research Setup”). Provide Up/Down controls to reorder, inline renaming, delete, and a live preview pane for the selected agent’s metadata. Whenever order or titles change and the user clicks **Save Agent** or **Run Agent**, regenerate ordinal labels so on-screen numbering and cached metadata remain in sync.
- Include a numeric `Loop limit` control (Spinbox or Slider) in the configuration header. Persist the chosen integer alongside the agent list so `_run_agent_sequence()` can enforce how many branch jumps are allowed per run. Default the value to `0` (no loops allowed); users can set it to `1` or higher when they explicitly want limited revisits.
- The footer hosts three primary buttons: **Load Agent** (pulls JSON from the persisted sequences folder), **Save Agent** (writes current list to disk), and **Run Agent** (auto-saves using the latest title before executing). Closing the window via the “x” simply returns to chat without altering staged agents.
- Selecting **Save Agent** commits changes but keeps the Configure Agents window open for further edits; only clicking **Run Agent** or the window close “X” will dismiss the dialog.

---

## 6. Staging Agents (Capture Workflow)

### Hook: `OllamaChat.send_message()`

1. Early in the method (after validation), check `if self.agent_mode_var.get():`
2. Build the agent payload instead of sending:
   - Extract user input (`original_input`).
  - Snapshot current system prompt (`self.system_text`), developer selection (`self.developer.get()`), chosen model, and every slider/toggle captured in the Agent Definition Schema (`parameters`, `tools`, inclusion flags, chat-history setting, etc.) so the exact state is preserved for both local and proprietary providers.
   - Capture derived artifacts (RAG file list, file attachments) by referencing existing instance vars (`self.file_content`, `self.rag_files`).
  - Record provider-specific metadata (e.g., Gemini safety settings, DeepSeek or Anthropic temperature overrides if exposed) and note the active API configuration so proprietary agents replay correctly without additional prompts.
   - Resolve auto-title: `First-Agent`, `Second-Agent`, etc. (use `len(self.armed_agents)` to determine ordinal).
   - Append to `self.armed_agents` and persist to temp cache.
3. UI feedback: `display_message(f"\n{label} Defined\n", 'status')`.
4. Optionally clear input but **do not** add the message to `ConversationManager` (to avoid polluting chat history). Instead log a status entry so the user sees confirmation.
5. Return early so the message is not sent to the model.

### Additional Considerations

- When Agent Mode is active, disable real-time streaming indicator to avoid confusion.
- Preserve per-agent file attachments by saving paths + extracted content. Use existing `self.file_content` for inclusion when executing later.
- For proprietary providers, surface a clear status message if required API credentials are missing at staging time and include that state in the agent metadata so the configuration window can flag issues before execution.

---

## 7. Executing the Sequence

### Trigger: `on_agent_mode_toggle(False)`

1. If `self.armed_agents` is empty, simply revert UI state and keep the Configure Agents button disabled.
2. When agents exist, emit `display_message(f"\n{len(self.armed_agents)} Agents Defined. View Configuration Window to Begin...\n", 'status')` and set the Configure Agents button to enabled so the user can review before running.
3. Persist the staged list to the temporary cache so the configuration window accurately reflects the latest ordering and metadata.

### Configure Window Commands

- **Load Agent** – opens a file picker rooted at the dedicated `agents/` folder, deserialises JSON into `self.armed_agents`, refreshes the UI list, and disables the Run button until at least one agent is present.
- **Save Agent** – writes the current in-memory list (with updated ordering, titles, and metadata) to the same folder. Updating titles here also cascades to ordinal labels and cached metadata.
- **Run Agent** – first performs the same save operation using the last chosen filename/title, then initiates execution. If no prior save name exists, prompt the user for one before proceeding.

### `_run_agent_sequence()` Flow

1. The Run command disables sidebar controls (Agent checkbox, Configure Agents button) and spawns a worker thread that calls `_run_agent_sequence()` to keep Tkinter responsive.
2. Iterate agents in order, resolving placeholders and reconstructing payloads exactly as `send_message()` would have produced (system prompt, user message, tool toggles, attachments).
3. Invoke `get_response()` (or a thin wrapper) for each agent, waiting for completion before moving to the next. Store each assistant output in `agent['result']` for downstream references.
4. Update the chat/status panel after every agent (`Agent {index} Task Complete...`). When all agents finish, post `All Agents Finished` and re-enable the Agent checkbox and Configure Agents button.
5. If the final agent writes files, rely on the existing Write File workflow for success notifications; optionally summarise file paths in the chat status message.

### Error Handling

- Wrap `_run_agent_sequence` in `safe_execute` or explicit try/except blocks. On failure, surface `display_message("Agent N failed: ...", 'error')`, log diagnostic context, and decide whether to halt or allow the user to adjust and rerun from the configuration window.

---

## 8. Cross-Agent Context Awareness

### Placeholder Strategy & Schema Review UI

- During staging, parse agent instructions for `{{Agent-X}}` patterns using regex and store a `references` array in agent metadata.
- In the Configure Agents window, list each agent as a collapsible row using the ordinal dropdown pattern (`Agent-1 >  |  ^  V`).
  - Clicking the `>` chevron expands the stored Agent Definition Schema for that agent (read-only view by default) and renders the entire JSON payload captured during staging so every field—title, developer, models, parameters, tools, metadata—is visible.
  - An **Edit schema?** toggle inside the expanded panel switches the layout into an editable text area pre-populated with that full JSON blob; the toggle label flips to **Save schema?** while editing so users can adjust any field before committing.
  - Selecting **Save schema?** writes the edits back into the in-memory schema (re-validating required fields) without closing the window and returns the toggle to the view state.
  - The `^` and `V` arrow buttons move the agent up or down in the sequence immediately, but final ordinal titles are recomputed only when **Save Agent** or **Run Agent** is clicked to avoid churn during experimentation.
- During execution, maintain a dictionary `resolved_outputs = {"Agent-1": "..."}` updated after each agent completes so placeholders can be resolved with the latest outputs.
- Before sending an agent’s message to `get_response`, replace placeholders with relevant summaries. Options for substitution:
  - **Full output** – direct insertion of prior agent assistant responses.
  - **Headline summary** – run a quick summarization using existing model (optional, to prevent context bloat).
  - **Control tokens** – replace with a short instruction like “see results from Agent-2 in previous message”.
- When constructing the next agent’s prompt, prepend a concise hand-off string such as `"Agent-1 produced the output: [...] You are Agent-2"`. If the previous agent emitted no textual reply (e.g., purely file operations), substitute an explicit placeholder (`"Agent-1 produced no direct response."`) while still passing along any file paths or metadata.
- Because all agents share the same working directory and tool stack, files written by one agent (via the Write File tool or direct file system access) remain immediately available. The next agent can reference those paths in its instructions or through placeholder substitution to load/read/update the artifacts without extra wiring.
- The Configure Agents window remains open after **Save Agent** so users can continue adjusting; it only dismisses when the window close button (`X`) or **Run Agent** is pressed. Renaming and movement operations are therefore transactional and visible before committing to a run.
- Because placeholders reference titles, letting users rename agents ensures clarity. Enforce unique titles within a sequence and warn if edits introduce collisions.

### Execution Branching

- The instructions mention conditional jumps (e.g., “If user completed this step, proceed to {{Agent-4}}”). Implement minimal support by interpreting directives post-response:
  - After each agent returns, scan its output for markers like `PROCEED: Agent-4` or `RETRY: Agent-2`.
  - Maintain a lightweight state machine inside `_run_agent_sequence()` to adjust the next index accordingly. Use a loop rather than a simple for loop to allow jumps.
  - Honor the configuration window’s loop limit so branch directives only fire when explicitly allowed (value `0` keeps execution linear).
- Example workflow (three agents):
  1. **Agent-1** instruction: “If you find `x`, do `y`. Output `y done` when successful; otherwise report `no x found`.”
  2. **Agent-2** listens for Agent-1’s hand-off string. If it contains `no x found`, Agent-2 performs task `z`; if it reads `y done`, Agent-2 simply updates its status and signals the engine to skip straight to Agent-3.
  3. **Agent-3** runs only after Agent-2 signals continuation.
- This branching mode is optional and expected to be used sparingly; the default remains a simple linear sequence where each agent executes exactly once in order.

```mermaid
flowchart TD
    Start([Start sequence]) --> A1[Agent-1 executes]
    A1 -->|Outputs "y done"| Check1{Did Agent-1 report "y done"?}
    A1 -->|Outputs "no x found"| Check1
    Check1 -->|Yes| Skip2[Skip Agent-2 tasks]
    Check1 -->|No| A2[Agent-2 executes task z]
    Skip2 --> Handoff2[Hand off to Agent-2 for status update only]
    Handoff2 --> A3[Agent-3 executes]
    A2 --> NextCheck{Should Agent-3 run?}
    NextCheck -->|Yes| A3
    NextCheck -->|No (loop/retry)| Retry2[Agent-2 retry or alternate path]
    Retry2 --> A2
    A3 --> End([All agents finished])
```

- The flowchart illustrates how the engine evaluates each agent’s output, conditionally triggers follow-on work, and still converges on the final agent. When no branching directives are present, the Start → Agent-1 → Agent-2 → Agent-3 → End path is followed without any decision nodes.

---

## 9. Saving & Loading Agent Sequences

### Fast Path – Adapt `PromptManager` Patterns (With Isolated Storage)

- Implement an `AgentSequenceStore` (embedded inside or alongside `PromptManager`) that mirrors its JSON read/write utilities but never touches the existing `prompts/` directory.
- Provide dedicated helpers, e.g. `save_agent_sequence(title, payload)`, `load_agent_sequence(title)`, `list_agent_sequences()`, each hard-wired to the `agents/` folder and a unique filename suffix (`.agent.json`) so collisions with prompt files are impossible.
- Continue using the same sanitisation logic (`{safe_title}`) for filenames, but keep the data model distinct (agent schemas vs. prompt text) to guarantee the two collections remain independent.

### UI Hooks

- **Startup handshake**: During application initialization, invoke `list_agent_sequences()` to determine whether any `.agent.json` files exist. If yes, hydrate `self.armed_agents` and (provided the Agent Tool isn’t currently capturing a new agent) enable the Configure Agents button immediately; otherwise leave it disabled until staging produces agents.
- **Configure Agents window**: When opened, populate the list from `self.armed_agents`. If the list is empty, display an inline hint explaining that agents must be defined or loaded before configuration is possible and keep action buttons disabled.
- **Save Agent**: Calls `save_agent_sequence()` with the active title/path, persists the latest in-memory list to `agents/`, refreshes the sidebar enablement state, and retains the window for further edits.
- **Load Agent**: Presents a file picker filtered to `agents/*.agent.json`, hydrates `self.armed_agents` with the chosen payload, refreshes labels/ordering, and posts a status message such as `Loaded sequence 'Research Workflow' (4 agents)`. The Run button remains disabled until the loaded list is non-empty.
- **Optional quick-access UI**: A read-only combobox in the sidebar can list saved agent sequences for rapid staging, but it should mirror the dedicated store to avoid confusion with prompt shortcuts.

### Auto-Save on Toggle Off

- When the user deactivates Agent Mode without running, prompt to save the staged sequence (similar to unsaved conversation prompts) to reduce accidental loss. The temporary cache should also mirror the configure-window contents so the user can immediately reopen and adjust.

---

## 10. Temporary Cache Mechanics

- Use `tempfile.gettempdir()` combined with a deterministic filename (`agent_sequence_cache.json`) to store the current `self.armed_agents` list as JSON so it can be inspected while the chatbot is open.
- Keep the cache in place until the user explicitly clears the session (via the existing **Clear Chat** control) or exits the application; both paths wipe the file and reset `self.armed_agents`.
- Continue relying on vanilla `json.dump`/`json.load`, matching existing patterns.

---

## 11. Telemetry & Logging

- Keep logging lightweight: the existing UI status messages (chat inserts, status bar text, configure window labels) are sufficient for day-to-day visibility.
- If deeper inspection is desired, users can open the temporary cache JSON while the chatbot remains running to review each agent’s captured output; no additional file logging is required.

---

## 12. Edge Cases & Safeguards

- **Toggle with no agents**: When the user turns the Agent Tool off without staging anyone, immediately return to the regular chat state—no pop-ups, no warnings, and titles reset as if the feature had never been engaged.
- **Missing models**: If a saved sequence references a model no longer available, surface a warning and allow the user to pick an alternative before execution.
- **Tool reuse**: The tool toggles already captured in each agent definition are replayed as-is during execution; there’s no separate prerequisite logic beyond recalling those saved settings.
- **Large sequences**: Cap maximum agents (e.g., 10) initially to ensure manageable UI and resource consumption.
- **Recursive branching**: Respect the user-selected loop limit captured in the configuration window. When the value is `0` (default) ignore all branch directives that would revisit a prior agent, ensuring a simple linear run. For limits `≥1`, track per-agent hop counts inside `_run_agent_sequence()` and allow at most that many revisits across the entire execution (e.g., `1` permits a single loop if the conditions request it). Pair this with a `visited[(agent_title, branch_marker)]` map; when either the global limit or a per-path threshold is exceeded, emit `display_message("Branch limit reached for Agent-2 → Agent-1", 'status')` and resume the remaining agents in their default order so the sequence always terminates.
- **Concurrency**: Ensure only one agent sequence can run at a time. Disable the Agent checkbox and Send button during execution.

---

## 13. Testing & Validation Plan

1. **Unit Tests (new)**
   - Extend existing test harness or add targeted scripts (e.g., verify agent payload serialization/deserialization using `PromptManager`).
   - Mock `_run_agent_sequence()` flow to confirm placeholder resolution and branching.
2. **Manual QA Checklist**
   - Baseline chat with Agent Tool unchecked.
   - Stage 1, 2, 5 agent sequences and execute.
   - Save, reload, and execute pre-defined sequences.
   - Validate UI messages appear in correct order.
   - Confirm Write File tool works inside agent runs.
   - Introduce an intentional failure (e.g., invalid API key for one agent) and verify error handling.
3. **Regression Tests**
   - Rerun existing flows (MCP retrieval, RAG ingestion, file drop) to ensure no regression when Agent Mode disabled.
4. **Performance Checks**
   - Time sequential execution of multiple agents to confirm no UI freezes (thanks to background threads).

---

## 14. Deployment & Rollout Strategy

- **Phase 0 – Feature Flag**: Keep the checkbox hidden behind a settings flag (e.g., `self.settings.get("agent_mode_beta", False)`) until QA completes.
- **Phase 1 – Beta Release**: Surface the checkbox with a “(beta)” tag, limit max agents to 3, gather feedback.
- **Phase 2 – Full Release**: After addressing feedback, remove beta label, expand to full feature set (saved sequences dropdown, branching, etc.).
- **Phase 3 – Advanced Enhancements**: Add analytics panel (reuse `ToolStatusPanel` to show agent durations, success rates) and deeper integration with RAG/MCP.

---

## 15. Implementation Checklist (Step-by-Step)

1. **Extend settings and core state**
    - Add persistent keys in `settings.py` for `agent_mode_enabled`, current loop limit, and any other defaults you plan to restore on startup.
    - In `OllamaChat.init_variables()` initialise `self.agent_mode_var`, `self.agent_loop_limit` (`tk.IntVar`), `self.armed_agents`, and `self.active_agent_sequence_name`, loading existing values through `Settings`.
    - Update `apply_settings()` (or equivalent) so toggling the checkbox or changing the loop limit immediately stores the latest preference.

2. **Wire deterministic cache helpers**
    - Create lightweight helpers (inside `OllamaChat` or a dedicated `agent_cache.py`) for `load_cached_agents()`, `save_agents_to_cache()`, and `clear_agent_cache()` that operate on the shared JSON file under `tempfile.gettempdir()`.
    - Invoke the loader during app startup; hydrate `self.armed_agents` and enable the Configure button if data exists and Agent Mode isn’t currently collecting new entries.
    - Ensure the cache is cleared whenever the user presses **Clear Chat**, exits the app, or runs a sequence successfully.

3. **Add Agent Mode controls to the sidebar**
    - In `create_sidebar()`, render the Agent Tool checkbox and disabled **Configure Agents** button within the existing Options frame; store the button widget on `self` for later state toggles.
    - Bind the checkbox to `self.on_agent_mode_toggle` and display the initial status message (`Agent Sequence: Begin`) when the mode is first activated.
    - On initial render, consult the cache helper to decide whether the Configure button should start enabled.

4. **Implement the toggle handler**
    - In `on_agent_mode_toggle()`, branch on the checkbox state: when turning on, reset in-memory lists, disable Configure, and emit the begin message; when turning off, persist staged agents, show the `N Agents Defined...` status, and re-enable Configure if agents exist.
    - Respect the “toggle with no agents” safeguard by returning straight to normal chat without prompts when `self.armed_agents` is empty.
    - Persist the updated loop limit and checkbox state through `Settings` each time the handler runs.

5. **Intercept `send_message()` for staging**
    - Early in the method, short-circuit when `self.agent_mode_var.get()` is true: assemble the agent schema (system text, user input, provider, parameters, tool flags, references, attachments).
    - Auto-assign titles and ordinals, append to `self.armed_agents`, call `save_agents_to_cache()`, and emit status updates (`First Agent Defined`, etc.).
    - Skip the normal conversation pipeline: clear the input widget, avoid appending to `ConversationManager`, and return immediately.

6. **Build the AgentSequence store**
    - Extend `prompt_manager.py` (or add `agent_sequence_store.py`) with helpers to `list`, `load`, `save`, and `delete` JSON files inside a new `agents/` directory using a `.agent.json` suffix.
    - Reuse filename sanitisation from `PromptManager`, but keep functions agent-specific to avoid collisions with prompt data.
    - Surface convenience wrappers on `OllamaChat` so the UI layer can call store operations without duplicating path logic.

7. **Construct the Configure Agents window**
    - Create a Toplevel that mirrors existing MCP UI patterns: listbox/treeview for agents, preview pane, inline rename controls, reorder buttons, delete action, and an editable JSON inspector (view/edit toggle).
    - Bind selection changes to refresh previews, and keep the window open until the user clicks **Run Agent** or closes it manually.
    - Add the numeric `Loop limit` control (Spinbox or Slider) at the top, backing it with `self.agent_loop_limit`; when the value changes, persist to settings and cache.

8. **Implement configuration commands**
    - Wire **Load Agent** to file-picker interactions that populate `self.armed_agents`, refresh list ordering, and update the Configure button state.
    - Wire **Save Agent** to call the AgentSequence store, recompute ordinal labels, persist the loop limit, and update cache data without closing the window.
    - Wire **Run Agent** to validate preconditions (non-empty list, credentials present), auto-save the latest sequence, disable sidebar controls, and dispatch `_run_agent_sequence()` on a worker thread.

9. **Execute sequences with branching awareness**
    - Inside `_run_agent_sequence()`, reconstruct each agent payload, call the appropriate `ModelManager.get_response()`, and collect outputs into `resolved_outputs` for placeholder substitution.
    - Before executing each agent, replace `{{Agent-X}}` tokens using the latest resolved outputs and include file paths for shared artifacts.
    - Enforce the user-defined loop limit: track total branch hops and `(agent_title, branch_marker)` visits, stop when limits are hit, emit a status message, and fall back to linear execution for the remaining agents.
    - Guard the flow with try/except, surface errors via `display_message(..., 'error')`, and always re-enable the sidebar controls in `finally`.

10. **Finalize UX, validation, and rollout hooks**
    - Ensure status bar text, chat inserts, and optional `ToolStatusPanel` updates reflect staging progress, loop-limit warnings, and completion.
    - Integrate cache clearing with the **Clear Chat** button and application shutdown, and respect concurrency by disabling Send/Agent controls while a run is active.
    - Implement automated/unit tests covering serialization, cache recovery, placeholder substitution, loop-limit enforcement, and Run button threading. Update the README and ship sample `.agent.json` files once all checks pass.

Each checklist item builds on prior work yet stays modular, enabling incremental commits and focused testing sessions.

---

## 16. Documentation & User Education

- Update `README.md` with a dedicated Agent Mode section (usage, sample workflows, limitations).
- Add inline tooltips in the UI (Tkinter `ttk.Tooltip` pattern already used elsewhere) explaining what happens when the Agent Tool is enabled.
- Provide sample saved sequences (JSON) under `prompts/` to illustrate best practices.

---

## 17. Future Enhancements (Post-MVP)

- **Visualization**: Reuse `RAGVisualizerPanel` aesthetics to display agent pipeline status (Gantt-like view).
- **Agent collaboration**: Let agents exchange structured outputs (JSON) via shared context objects persisted in `resolved_outputs`.
- **Concurrency options**: After MVP, allow parallel agents (queue-based using `ToolsManager`), while keeping sequential mode default.
- **Auto-summarization**: Use the final agent to summarize the entire sequence and save as a conversation transcript automatically.

---

By following this plan, Agent Mode can be added without rewriting existing systems, preserving current workflows while enabling powerful staged, multi-agent operations for advanced users.
