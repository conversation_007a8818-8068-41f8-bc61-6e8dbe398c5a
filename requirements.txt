# AI and Language Processing
openai
langchain
langchain-text-splitters
chromadb
sentence-transformers>=2.2.0
transformers>=4.0.0
google-generativeai
nltk

# Data processing and analysis
numpy
pandas
scipy
scikit-learn

# Web and API
flask
flask-cors
fastapi
uvicorn
requests
httpx

# Document processing
python-docx
PyPDF2
python-pptx
markdown-it-py
pdfminer.six

# Utilities
python-dotenv
tqdm
rich
pillow
pydantic

# Audio and speech
librosa
pyttsx3
SpeechRecognition

# Visualization
matplotlib

# Database
sqlalchemy

# GUI
# tkinter is part of the Python standard library
tkinterdnd2

# Model providers
ollama
anthropic
# No official deepseek package yet - implemented via API calls

# File processing
markitdown[all]  # Includes all optional dependencies
markdown

# Web search
requests
beautifulsoup4
crawl4ai  # Advanced web search and crawling

# Optional specific dependencies (included in markitdown[all])
# markitdown[pdf,docx,pptx,xlsx,xls]  # Document processing
# markitdown[audio-transcription]  # Audio transcription
# markitdown[youtube-transcription]  # YouTube content extraction
# markitdown[az-doc-intel]  # Azure Document Intelligence
