"""
MCP UI Enhanced - Backward Compatible UI Wrapper  
===============================================

This module provides enhanced UI functionality while maintaining 100% backward
compatibility with the existing MCPPanel. Uses the composite pattern to add
enhanced features without modifying the original UI implementation.

SAFETY FEATURES:
- Wraps original MCPPanel (zero UI breakage)
- All enhancements are optional additional tabs/panels
- Falls back to original UI if enhancements fail
- Maintains exact initialization parameters for compatibility
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import Dict, List, Optional, Any
import json
import time
from datetime import datetime

# Import original UI classes for compatibility
from mcp_ui import MCPPanel
from mcp_manager_enhanced import MCPManagerEnhanced

class MCPPanelEnhanced:
    """
    Enhanced MCP Panel that wraps original functionality.
    Maintains 100% backward compatibility while adding new features.
    """
    
    def __init__(self, parent, mcp_manager, bg_color, fg_color, accent_color, secondary_bg):
        """Initialize enhanced panel with same parameters as original."""
        
        # Store initialization parameters
        self.parent = parent
        self.mcp_manager = mcp_manager
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.accent_color = accent_color
        self.secondary_bg = secondary_bg
        
        # Initialize original panel first (critical for compatibility)
        self.original_panel = MCPPanel(parent, mcp_manager, bg_color, fg_color, accent_color, secondary_bg)
        
        # Enhanced UI state
        self.enhanced_mode = False
        self.enhanced_widgets = {}
        self.feature_flags = {
            "enhanced_search": False,
            "memory_stats": False,
            "auto_categorization_ui": False,
            "enhanced_filters": False
        }
        
        # Current filters
        self.current_filters = {
            "memory_type": "all",
            "priority": "all", 
            "date_range": "all"
        }
        
        # Create enhanced UI container (initially hidden)
        self.enhanced_container = None
        self._create_enhanced_ui()
    
    # ==========================================================================
    # BACKWARD COMPATIBLE API (Exact Same Methods)
    # ==========================================================================
    
    def show(self):
        """Show the panel (enhanced or original based on mode)."""
        try:
            if self.enhanced_mode and self.enhanced_container:
                self.original_panel.show()  # Always show original
                self.enhanced_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                print("Enhanced MCP UI loaded")
            else:
                self.original_panel.show()  # Use original implementation
                
        except Exception as e:
            print(f"Enhanced UI failed, using original: {e}")
            self.original_panel.show()
    
    def hide(self):
        """Hide the panel."""
        try:
            if self.enhanced_container:
                self.enhanced_container.pack_forget()
            self.original_panel.hide()
            
        except Exception as e:
            print(f"Enhanced hide failed, using original: {e}")
            self.original_panel.hide()
    
    # ==========================================================================
    # ENHANCED FUNCTIONALITY (New Features)
    # ==========================================================================
    
    def _create_enhanced_ui(self):
        """Create enhanced UI components as additional features."""
        try:
            # Create main enhanced container
            self.enhanced_container = ttk.Frame(self.parent)
            
            # Create notebook for tabbed interface
            self.notebook = ttk.Notebook(self.enhanced_container)
            self.notebook.pack(fill=tk.BOTH, expand=True)
            
            # Tab 1: Enhanced Memory Management
            self._create_memory_management_tab()
            
            # Tab 2: Statistics and Analytics  
            self._create_statistics_tab()
            
            # Tab 3: Advanced Search and Filters
            self._create_search_tab()
            
            # Tab 4: Settings and Configuration
            self._create_settings_tab()
            
        except Exception as e:
            print(f"Enhanced UI creation failed: {e}")
            self.enhanced_container = None
    
    def _create_memory_management_tab(self):
        """Create enhanced memory management tab."""
        try:
            memory_frame = ttk.Frame(self.notebook)
            self.notebook.add(memory_frame, text="📚 Memories")
            
            # Top controls
            controls_frame = ttk.Frame(memory_frame)
            controls_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # Add memory button with enhanced features
            ttk.Button(
                controls_frame, 
                text="➕ Add Memory",
                command=self._add_memory_enhanced
            ).pack(side=tk.LEFT, padx=(0, 5))
            
            # Memory type selector
            ttk.Label(controls_frame, text="Type:").pack(side=tk.LEFT, padx=(10, 5))
            self.type_var = tk.StringVar(value="note")
            type_combo = ttk.Combobox(
                controls_frame,
                textvariable=self.type_var,
                values=["note", "task", "reference", "document", "code_snippet", "learning"],
                state="readonly",
                width=12
            )
            type_combo.pack(side=tk.LEFT, padx=(0, 5))
            
            # Priority selector
            ttk.Label(controls_frame, text="Priority:").pack(side=tk.LEFT, padx=(10, 5))
            self.priority_var = tk.StringVar(value="normal")
            priority_combo = ttk.Combobox(
                controls_frame,
                textvariable=self.priority_var,
                values=["low", "normal", "high"],
                state="readonly",
                width=8
            )
            priority_combo.pack(side=tk.LEFT, padx=(0, 5))
            
            # Refresh button
            ttk.Button(
                controls_frame,
                text="🔄 Refresh", 
                command=self._refresh_memory_list
            ).pack(side=tk.RIGHT)
            
            # Memory list with enhanced display
            list_frame = ttk.Frame(memory_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # Create treeview for better display
            columns = ("content", "type", "priority", "tags", "created")
            self.memory_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
            
            # Configure columns
            self.memory_tree.heading("content", text="Content")
            self.memory_tree.heading("type", text="Type")
            self.memory_tree.heading("priority", text="Priority")
            self.memory_tree.heading("tags", text="Tags")
            self.memory_tree.heading("created", text="Created")
            
            # Configure column widths
            self.memory_tree.column("content", width=300)
            self.memory_tree.column("type", width=80)
            self.memory_tree.column("priority", width=60)
            self.memory_tree.column("tags", width=150)
            self.memory_tree.column("created", width=100)
            
            # Scrollbar for treeview
            tree_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memory_tree.yview)
            self.memory_tree.configure(yscrollcommand=tree_scroll.set)
            
            # Pack treeview and scrollbar
            self.memory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Context menu for memory operations
            self.memory_tree.bind("<Button-3>", self._show_memory_context_menu)
            
        except Exception as e:
            print(f"Memory management tab creation failed: {e}")
    
    def _create_statistics_tab(self):
        """Create statistics and analytics tab."""
        try:
            stats_frame = ttk.Frame(self.notebook)
            self.notebook.add(stats_frame, text="📊 Statistics")
            
            # Stats display
            self.stats_text = tk.Text(
                stats_frame,
                wrap=tk.WORD,
                height=20,
                font=("Consolas", 10)
            )
            self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # Refresh stats button
            ttk.Button(
                stats_frame,
                text="🔄 Refresh Statistics",
                command=self._refresh_statistics
            ).pack(pady=5)
            
        except Exception as e:
            print(f"Statistics tab creation failed: {e}")
    
    def _create_search_tab(self):
        """Create advanced search and filters tab."""
        try:
            search_frame = ttk.Frame(self.notebook)
            self.notebook.add(search_frame, text="🔍 Search")
            
            # Search controls
            search_controls = ttk.Frame(search_frame)
            search_controls.pack(fill=tk.X, padx=5, pady=5)
            
            # Search entry
            ttk.Label(search_controls, text="Search:").pack(side=tk.LEFT)
            self.search_var = tk.StringVar()
            search_entry = ttk.Entry(search_controls, textvariable=self.search_var, width=40)
            search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            search_entry.bind("<Return>", lambda e: self._perform_search())
            
            # Search button
            ttk.Button(
                search_controls,
                text="🔍 Search",
                command=self._perform_search
            ).pack(side=tk.RIGHT)
            
            # Filters frame
            filters_frame = ttk.LabelFrame(search_frame, text="Filters")
            filters_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # Filter controls in grid
            ttk.Label(filters_frame, text="Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
            self.filter_type_var = tk.StringVar(value="all")
            ttk.Combobox(
                filters_frame,
                textvariable=self.filter_type_var,
                values=["all", "note", "task", "reference", "document"],
                state="readonly"
            ).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
            
            ttk.Label(filters_frame, text="Priority:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
            self.filter_priority_var = tk.StringVar(value="all")
            ttk.Combobox(
                filters_frame,
                textvariable=self.filter_priority_var,
                values=["all", "low", "normal", "high"],
                state="readonly"
            ).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
            
            # Search results
            results_frame = ttk.Frame(search_frame)
            results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            self.search_results = tk.Text(
                results_frame,
                wrap=tk.WORD,
                height=15,
                font=("Arial", 10)
            )
            search_scroll = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.search_results.yview)
            self.search_results.configure(yscrollcommand=search_scroll.set)
            
            self.search_results.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            search_scroll.pack(side=tk.RIGHT, fill=tk.Y)
            
        except Exception as e:
            print(f"Search tab creation failed: {e}")
    
    def _create_settings_tab(self):
        """Create settings and configuration tab."""
        try:
            settings_frame = ttk.Frame(self.notebook)
            self.notebook.add(settings_frame, text="⚙️ Settings")
            
            # Feature toggles
            features_frame = ttk.LabelFrame(settings_frame, text="Enhanced Features")
            features_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # Auto-categorization toggle
            self.auto_cat_var = tk.BooleanVar()
            ttk.Checkbutton(
                features_frame,
                text="Auto-categorization",
                variable=self.auto_cat_var,
                command=self._toggle_auto_categorization
            ).pack(anchor=tk.W, padx=10, pady=5)
            
            # Enhanced analytics toggle
            self.analytics_var = tk.BooleanVar()
            ttk.Checkbutton(
                features_frame,
                text="Enhanced Analytics",
                variable=self.analytics_var,
                command=self._toggle_analytics
            ).pack(anchor=tk.W, padx=10, pady=5)
            
            # UI Mode toggle
            mode_frame = ttk.LabelFrame(settings_frame, text="UI Mode")
            mode_frame.pack(fill=tk.X, padx=5, pady=5)
            
            ttk.Button(
                mode_frame,
                text="Switch to Classic Mode",
                command=self._switch_to_classic
            ).pack(padx=10, pady=5)
            
            # Status display
            status_frame = ttk.LabelFrame(settings_frame, text="Status")
            status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            self.status_text = tk.Text(
                status_frame,
                wrap=tk.WORD,
                height=10,
                font=("Consolas", 9)
            )
            self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # Update status
            self._update_status()
            
        except Exception as e:
            print(f"Settings tab creation failed: {e}")
    
    # ==========================================================================
    # ENHANCED FUNCTIONALITY IMPLEMENTATIONS
    # ==========================================================================
    
    def _add_memory_enhanced(self):
        """Add memory with enhanced options."""
        try:
            # Create custom dialog for enhanced memory input
            dialog = tk.Toplevel(self.parent)
            dialog.title("Add Enhanced Memory")
            dialog.geometry("500x400")
            dialog.transient(self.parent)
            dialog.grab_set()
            
            # Content input
            ttk.Label(dialog, text="Content:").pack(anchor=tk.W, padx=10, pady=5)
            content_text = tk.Text(dialog, height=8, width=60)
            content_text.pack(padx=10, pady=5)
            
            # Tags input
            ttk.Label(dialog, text="Tags (comma separated):").pack(anchor=tk.W, padx=10)
            tags_entry = ttk.Entry(dialog, width=60)
            tags_entry.pack(padx=10, pady=5)
            
            # Buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=10)
            
            def save_memory():
                content = content_text.get(1.0, tk.END).strip()
                if not content:
                    messagebox.showerror("Error", "Content cannot be empty")
                    return
                
                tags = [tag.strip() for tag in tags_entry.get().split(",") if tag.strip()]
                
                # Use enhanced manager if available
                if isinstance(self.mcp_manager, MCPManagerEnhanced):
                    memory_id = self.mcp_manager.add_memory(content, tags)
                else:
                    memory_id = self.mcp_manager.add_memory(content, tags)
                
                if memory_id:
                    messagebox.showinfo("Success", f"Memory added: {memory_id}")
                    self._refresh_memory_list()
                    dialog.destroy()
                else:
                    messagebox.showerror("Error", "Failed to add memory")
            
            ttk.Button(button_frame, text="Save", command=save_memory).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open add memory dialog: {e}")
    
    def _refresh_memory_list(self):
        """Refresh the enhanced memory list."""
        try:
            # Clear current items
            for item in self.memory_tree.get_children():
                self.memory_tree.delete(item)
            
            # Get all memories
            memories = self.mcp_manager.get_all_memories()
            
            for memory in memories:
                # Extract enhanced fields if available
                content = memory.get('content', '')[:50] + ('...' if len(memory.get('content', '')) > 50 else '')
                memory_type = memory.get('memory_type', 'note')
                priority = str(memory.get('priority', 'normal'))
                tags = ', '.join(memory.get('tags', []))
                created = datetime.fromtimestamp(memory.get('created_at', 0)).strftime('%m/%d %H:%M')
                
                self.memory_tree.insert('', tk.END, values=(content, memory_type, priority, tags, created))
            
        except Exception as e:
            print(f"Memory list refresh failed: {e}")
    
    def _refresh_statistics(self):
        """Refresh statistics display."""
        try:
            if isinstance(self.mcp_manager, MCPManagerEnhanced):
                stats = self.mcp_manager.get_enhanced_stats()
                features = self.mcp_manager.get_feature_status()
            else:
                stats = {"total_memories": len(self.mcp_manager.get_all_memories())}
                features = {"enhanced_features": "not_available"}
            
            # Format statistics
            stats_text = "=== MCP STATISTICS ===\\n\\n"
            for key, value in stats.items():
                stats_text += f"{key.replace('_', ' ').title()}: {value}\\n"
            
            stats_text += "\\n=== FEATURE STATUS ===\\n\\n"
            for feature, status in features.items():
                stats_text += f"{feature.replace('_', ' ').title()}: {'ON' if status else 'OFF'}\\n"
            
            # Update display
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, f"Statistics refresh failed: {e}")
    
    def _perform_search(self):
        """Perform enhanced search."""
        try:
            query = self.search_var.get().strip()
            if not query:
                self.search_results.delete(1.0, tk.END)
                self.search_results.insert(1.0, "Please enter a search query.")
                return
            
            # Perform search
            results = self.mcp_manager.search_memories(query, 10)
            
            # Format results
            if results:
                results_text = f"=== SEARCH RESULTS FOR '{query}' ===\\n\\n"
                for i, memory in enumerate(results, 1):
                    results_text += f"{i}. {memory.get('content', '')[:100]}...\\n"
                    results_text += f"   Tags: {', '.join(memory.get('tags', []))}\\n"
                    results_text += f"   Type: {memory.get('memory_type', 'note')}\\n\\n"
            else:
                results_text = f"No results found for '{query}'"
            
            # Update display
            self.search_results.delete(1.0, tk.END)
            self.search_results.insert(1.0, results_text)
            
        except Exception as e:
            self.search_results.delete(1.0, tk.END)
            self.search_results.insert(1.0, f"Search failed: {e}")
    
    def _show_memory_context_menu(self, event):
        """Show context menu for memory operations."""
        # Placeholder for context menu implementation
        pass
    
    def _toggle_auto_categorization(self):
        """Toggle auto-categorization feature."""
        if isinstance(self.mcp_manager, MCPManagerEnhanced):
            if self.auto_cat_var.get():
                self.mcp_manager.enable_feature("auto_categorization")
            else:
                self.mcp_manager.disable_feature("auto_categorization")
            self._update_status()
    
    def _toggle_analytics(self):
        """Toggle enhanced analytics feature."""
        if isinstance(self.mcp_manager, MCPManagerEnhanced):
            if self.analytics_var.get():
                self.mcp_manager.enable_feature("enhanced_analytics")
            else:
                self.mcp_manager.disable_feature("enhanced_analytics")
            self._update_status()
    
    def _switch_to_classic(self):
        """Switch to classic UI mode."""
        self.enhanced_mode = False
        self.hide()
        self.show()  # This will show original UI
    
    def _update_status(self):
        """Update status display."""
        try:
            if hasattr(self, 'status_text'):
                status_info = f"Enhanced MCP UI Active\\n"
                status_info += f"Manager Type: {type(self.mcp_manager).__name__}\\n"
                
                if isinstance(self.mcp_manager, MCPManagerEnhanced):
                    features = self.mcp_manager.get_feature_status()
                    status_info += f"\\nFeatures:\\n"
                    for feature, enabled in features.items():
                        status_info += f"  {feature}: {'ON' if enabled else 'OFF'}\\n"
                
                self.status_text.delete(1.0, tk.END)
                self.status_text.insert(1.0, status_info)
        except:
            pass
    
    # ==========================================================================
    # FEATURE MANAGEMENT
    # ==========================================================================
    
    def enable_enhanced_mode(self):
        """Enable enhanced UI mode."""
        self.enhanced_mode = True
        print("Enhanced UI mode enabled")
    
    def disable_enhanced_mode(self):
        """Disable enhanced UI mode (use classic UI)."""
        self.enhanced_mode = False
        print("Enhanced UI mode disabled - using classic UI")
    
    def is_enhanced_mode(self) -> bool:
        """Check if enhanced mode is active."""
        return self.enhanced_mode

# =============================================================================
# COMPATIBILITY TESTING FUNCTIONS  
# =============================================================================

def test_ui_compatibility():
    """Test that enhanced UI maintains compatibility with original."""
    print("Testing UI compatibility...")
    
    # This would normally be tested with actual Tkinter integration
    # For now, just test class initialization
    
    try:
        # Mock the required parameters
        class MockParent:
            pass
        
        class MockManager:
            def get_all_memories(self):
                return []
        
        parent = MockParent()
        manager = MockManager()
        
        # Test enhanced panel initialization
        enhanced_panel = MCPPanelEnhanced(
            parent=parent,
            mcp_manager=manager, 
            bg_color="#2b2b2b",
            fg_color="#ffffff",
            accent_color="#007acc",
            secondary_bg="#3c3c3c"
        )
        
        print("✓ Enhanced UI initialization successful")
        print("✓ Original panel wrapper working")
        print("✓ Enhanced features available but disabled by default")
        
        return True
        
    except Exception as e:
        print(f"✗ UI compatibility test failed: {e}")
        return False

if __name__ == "__main__":
    test_ui_compatibility()